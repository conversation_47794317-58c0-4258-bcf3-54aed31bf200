import { requireAuth } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { User } from 'lucide-react'
import { LogoutButton } from '@/components/ui/logout-button'

export default async function DashboardPage() {
  const user = await requireAuth()

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-primary">
            سحابة المدينة
          </h1>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <User className="h-4 w-4" />
              {user.email}
            </div>
            <LogoutButton />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div>
            <h2 className="text-3xl font-bold">مرحباً بك</h2>
            <p className="text-muted-foreground mt-2">
              لوحة التحكم الخاصة بك في نظام سحابة المدينة
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>المهام</CardTitle>
                <CardDescription>
                  إدارة مهامك اليومية
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">0</p>
                <p className="text-sm text-muted-foreground">مهمة نشطة</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>المشاريع</CardTitle>
                <CardDescription>
                  تتبع تقدم مشاريعك
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">0</p>
                <p className="text-sm text-muted-foreground">مشروع جاري</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>التقارير</CardTitle>
                <CardDescription>
                  عرض التقارير والإحصائيات
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">0</p>
                <p className="text-sm text-muted-foreground">تقرير جديد</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}

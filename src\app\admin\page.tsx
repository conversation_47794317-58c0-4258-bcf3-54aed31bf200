import { requireAdmin } from '@/lib/auth'
import { User } from 'lucide-react'
import { AdminDashboard } from '@/components/admin/AdminDashboard'
import { LogoutButton } from '@/components/ui/logout-button'

export default async function AdminPage() {
  const profile = await requireAdmin()

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-primary">
            سحابة المدينة - لوحة الإدارة
          </h1>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <User className="h-4 w-4" />
              {profile.full_name || profile.email}
            </div>
            <LogoutButton />
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div>
            <h2 className="text-3xl font-bold">مرحباً بك في لوحة الإدارة</h2>
            <p className="text-muted-foreground mt-2">
              إدارة النظام والمستخدمين
            </p>
          </div>

          <AdminDashboard />
        </div>
      </main>
    </div>
  )
}
